<template>
  <div style="height: 100%; margin-left: 0px; margin-top: 0px; background-color: red">
    <VxeTable
      ref="vxeTableRef"
      size="small"
      :isShowTableHeader="false"
      :isDrop="false"
      :columns="columns"
      :tableData="allData"
      :loading="false"
      :tablePage="false"
    ></VxeTable>
    <div class="curve-panel" v-if="!!activeProcess">
      <div class="header">
        <div class="icon">{{ activeProcess?.chProjectName?.slice(0, 1) }}</div>
        <div class="name">{{ activeProcess?.projectName }}</div>
        <div class="close" @click="activeProcess = null">X</div>
      </div>
      <div class="chart"><ProcessChart style="margin-right: 20px" :dataSource="activeProcess" /></div>
    </div>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'
  import { loading } from 'vxe-pc-ui'
  // import { getChSimResList } from '../services.js'
  export default {
    name: 'ResultTable',
    props: ['dataSource'],
    components: { VxeTable },
    data() {
      return {
        active: undefined,
        allData: [],
        activeProcess: null,
        activeProcessShow: false,
        columns: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '调度对象',
            fixed: 'left',
            minWidth: 120,
            slots: {
              default: ({ row }) => {
                return (
                  <span>
                    {
                      <span>
                        <a onClick={() => this.projectClick(row)}>{row.projectId}</a>
                      </span>
                    }
                  </span>
                )
              },
            },
          },
          {
            field: 'parentProjectId',
            title: '所属干渠',
            minWidth: 80,
          },
          {
            field: 'startUpWlv',
            title: '初始上游水位(m)',
            minWidth: 120,
          },
          {
            field: 'startDownWlv',
            title: '初始下游水位(m)',
            minWidth: 120,
          },
          {
            field: 'endUpWlv',
            title: '末期上游水位(m)',
            minWidth: 120,
          },
          {
            field: 'endDownWlv',
            title: '末期下游水位(m)',
            minWidth: 120,
          },
          {
            field: 'totalFlow',
            title: '累计过闸流量(万m³)',
            minWidth: 150,
          }
        ]
      }
    },
    computed: {},
    watch: {
      dataSource: {
        handler(newVal, oldVal) {
          // this.active = newVal?.[0]?.projectId
          this.allData = newVal
        },
        immediate: true,
      },
    },
    created() {},
    methods: {
      projectClick(item) {
        this.activeProcessShow = true
        // getChSimResList({ chSimId: this.chSimId, projectId: item.projectId }).then(res => {
        //   let obj = res.data[0]
        //   this.activeProcess = { ...item, ...obj, chartData: res.data }
        // })
      },
    },
  }
</script>

<style lang="less" scoped>
  :deep(.ant-table-thead > tr > th) {
    background-color: #f2f3f5;
    padding: 10px 4px;
    font-size: 13px;
    font-weight: 500;
    height: 100px;
  }
  .curve-panel {
    position: absolute;
    z-index: 99999999;
    bottom: 34%;
    right: 8px;
    width: 580px;
    display: flex;
    border-radius: 4px;
    overflow: hidden;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    border: 2px solid rgba(78, 89, 105, 0.3);
    .header {
      background: #f2f3f5;
      color: #1d2129;
      line-height: 20px;
      padding: 6px 8px;
      display: flex;
      // align-items: center;

      .icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }
      .name {
        flex: 1;
        font-weight: 600;
        margin: 0 0 0 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .close {
        margin-left: auto;
        cursor: pointer;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        color: #8f959e;
      }
    }
    .chart {
      flex: 1;
      padding-top: 10px;
    }
  }
</style>
