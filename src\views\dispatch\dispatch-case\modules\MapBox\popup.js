import Vue from 'vue'
import PopupContent from './PopupContent.vue'
import mapboxgl from 'mapbox-gl'

export function mapboxPopup(mapIns, item) {
  const PopupContentItem = Vue.extend(PopupContent)

  const vIns = new PopupContentItem({
    propsData: { item },
  })
  vIns.$mount()
  const popupTemp = vIns.$el

  const option = {
    closeOnClick: false,
    closeButton: false,
    offset: [0, 0],
  }

  return new mapboxgl.Popup(option).setLngLat(item.lngLat).setDOMContent(popupTemp).addTo(mapIns)
}
