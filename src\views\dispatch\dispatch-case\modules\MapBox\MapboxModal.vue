<template>
  <ant-modal
    :visible="open"
    modal-title="调整位置"
    :loading="modalLoading"
    modalWidth="1000"
    modalHeight="760"
    @onFullScreen="onFullScreen"
    @cancel="cancel"
  >
    <div slot="content" layout="vertical" class="mapbox-content">
      <MapBox :options="longitude && latitude ? { center: [longitude, latitude] } : {}" @onMapMounted="onMapMounted" />
      <div class="address-box" v-if="location">
        <img src="~@/assets/images/poi-marker-default.png" alt="" />
        &nbsp;
        <div class="address">{{ location }}</div>
      </div>

      <div style="position: absolute; right: 16px; bottom: 16px">
        <MapStyle v-if="!!mapIns" :mapIns="mapIns" ref="mapStyleRef" />
      </div>
    </div>

    <template slot="footer">
      <a-button @click="cancel" style="margin-right: 10px">取消</a-button>
      <a-button type="primary" @click="confirm">确认</a-button>
    </template>
  </ant-modal>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import mapboxgl from 'mapbox-gl'
  import MapBox from '@/components/MapBox'
  import gcoord from 'gcoord'
  import MapStyle from '@/components/MapBox/MapStyle.vue'

  export default {
    name: 'MapModal',
    props: {},
    components: { AntModal, MapBox, MapStyle },
    data() {
      return {
        modalLoading: false,
        // 表单参数
        open: false,
        mapIns: null,
        markerIns: null,
        location: '',
        longitude: undefined,
        latitude: undefined,
      }
    },
    created() {},
    mounted() {},
    computed: {},
    watch: {},
    methods: {
      onMapMounted(mapIns) {
        this.mapIns = mapIns
        // this.modalLoading = false

        // 回显
        if (this.longitude && this.latitude) {
          this.mapIns.setCenter(gcoord.transform([+this.longitude, +this.latitude], gcoord.GCJ02, gcoord.WGS84))
          this.addressAnalysis({ type: 'getAddress', lngLat: [this.longitude, this.latitude] }, () => {
            this.addMarker([this.longitude, this.latitude])
          })
        } else {
          if (this.location) {
            this.addressAnalysis({ type: 'getLngLat', address: this.location }, () => {
              this.addMarker([this.longitude, this.latitude])
              this.mapIns.setCenter(gcoord.transform([+this.longitude, +this.latitude], gcoord.GCJ02, gcoord.WGS84))
            })
          }
        }

        this.mapIns.on('click', e => {
          if (this.markerIns) {
            this.markerIns.remove()
          }

          const lngLat = gcoord.transform([e.lngLat.lng, e.lngLat.lat], gcoord.WGS84, gcoord.GCJ02)
          this.longitude = lngLat[0]
          this.latitude = lngLat[1]
          this.addMarker(lngLat)
          this.mapIns.setCenter([e.lngLat.lng, e.lngLat.lat])
          this.addressAnalysis({ type: 'getAddress', lngLat })
        })
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 确认
      confirm() {
        this.open = false

        const lngLat = gcoord.transform([this.longitude, this.latitude], gcoord.GCJ02, gcoord.WGS84)

        this.$emit('confirm', { longitude: lngLat[0], latitude: lngLat[1], location: this.location })
      },
      /** 打开 */
      handleOpen(mapInfo) {
        this.open = true
        this.latitude = undefined
        this.latitude = undefined
        this.location = ''
        if (this.markerIns) {
          this.markerIns.remove()
        }

        // this.modalLoading = true
        if (mapInfo?.longitude && mapInfo?.latitude) {
          const lngLat = gcoord.transform([mapInfo.longitude, mapInfo.latitude], gcoord.WGS84, gcoord.GCJ02)
          this.longitude = lngLat[0]
          this.latitude = lngLat[1]
        }
        this.location = mapInfo.location

        if (this.mapIns) {
          this.onMapMounted(this.mapIns)
        }
      },
      onFullScreen() {
        this.mapIns.resize()
      },

      addMarker(lngLat) {
        this.markerIns = new mapboxgl.Marker({
          color: '#3D93FD',
          scale: 0.8,
        })
          .setLngLat(gcoord.transform(lngLat, gcoord.GCJ02, gcoord.WGS84))
          .addTo(this.mapIns)
      },
      // (逆)地址解析
      addressAnalysis(info, callback) {
        window.AMap.plugin(['AMap.Geocoder'], () => {
          //加载地理编码插件
          let geocoder = new AMap.Geocoder()
          if (info.type === 'getLngLat') {
            //返回地理编码结果
            geocoder.getLocation(info.address, (status, result) => {
              if (status === 'complete' && result.geocodes.length) {
                const location = result.geocodes[0].location
                this.longitude = location.lng
                this.latitude = location.lat
                callback && callback()
              } else {
                console.error('根据地址查询位置失败')
              }
            })
          }
          if (info.type === 'getAddress') {
            //逆地理编码
            geocoder.getAddress(info.lngLat, (status, result) => {
              if (status === 'complete' && result.regeocode) {
                let address = result.regeocode.formattedAddress
                this.location = address
                callback && callback()
              } else {
                console.error('根据经纬度查询地址失败')
              }
            })
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  @import url('~@/global.less');

  ::v-deep .ant-modal-body {
    padding: 10px;
    .modal-content {
      width: 100%;
      height: 100%;
      .mapbox-content {
        width: 100%;
        height: 100%;
        position: relative;
        .address-box {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translate(-50%);
          background-color: #fff;
          padding: 5px 10px;
          z-index: 1;
          display: flex;
          align-items: center;
          > img {
            width: 16px;
            height: 20px;
          }
          > .address {
            flex: 1;
            font-weight: 600;
          }
        }
      }
    }
  }
</style>
